{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/food/allrecipes-clone/src/app/loading.tsx"], "sourcesContent": ["import React from 'react';\n\nexport default function Loading() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <div className=\"text-center\">\n        {/* Animated Chef Hat */}\n        <div className=\"relative mb-8\">\n          <div className=\"w-16 h-16 mx-auto\">\n            <div className=\"absolute inset-0 bg-orange-600 rounded-full animate-ping opacity-75\"></div>\n            <div className=\"relative bg-orange-600 rounded-full w-16 h-16 flex items-center justify-center\">\n              <svg\n                className=\"w-8 h-8 text-white\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 20 20\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n              >\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V4a2 2 0 00-2-2H6zm1 2a1 1 0 000 2h6a1 1 0 100-2H7zm6 7a1 1 0 11-2 0 1 1 0 012 0zM7 10a1 1 0 000 2h2a1 1 0 100-2H7z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        {/* Loading Text */}\n        <h2 className=\"text-2xl font-semibold text-gray-900 mb-2\">\n          Cooking up something delicious...\n        </h2>\n        <p className=\"text-gray-600\">\n          Please wait while we prepare your recipes\n        </p>\n\n        {/* Loading Dots */}\n        <div className=\"flex justify-center space-x-1 mt-6\">\n          <div className=\"w-2 h-2 bg-orange-600 rounded-full animate-bounce\"></div>\n          <div className=\"w-2 h-2 bg-orange-600 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n          <div className=\"w-2 h-2 bg-orange-600 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,MAAK;oCACL,SAAQ;oCACR,OAAM;8CAEN,cAAA,8OAAC;wCACC,UAAS;wCACT,GAAE;wCACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQnB,8OAAC;oBAAG,WAAU;8BAA4C;;;;;;8BAG1D,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;8BAK7B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;4BAAoD,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;sCACnG,8OAAC;4BAAI,WAAU;4BAAoD,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;;;;;;;;;;;;;;;;;;AAK7G", "debugId": null}}]}