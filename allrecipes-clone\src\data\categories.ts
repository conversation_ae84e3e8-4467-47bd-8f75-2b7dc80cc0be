import { Category } from '@/types';

export const categories: Category[] = [
  {
    id: '1',
    name: 'Dinners',
    slug: 'dinners',
    description: 'Delicious dinner recipes for every occasion',
    image: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400&h=250&fit=crop',
    recipeCount: 1250
  },
  {
    id: '2',
    name: 'Breakfast & Brunch',
    slug: 'breakfast-brunch',
    description: 'Start your day with amazing breakfast recipes',
    image: 'https://images.unsplash.com/photo-1533089860892-a7c6f0a88666?w=400&h=250&fit=crop',
    recipeCount: 850
  },
  {
    id: '3',
    name: 'Lunch',
    slug: 'lunch',
    description: 'Quick and satisfying lunch ideas',
    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=250&fit=crop',
    recipeCount: 650
  },
  {
    id: '4',
    name: 'Appetizers & Snacks',
    slug: 'appetizers-snacks',
    description: 'Perfect starters and snacks for any gathering',
    image: 'https://images.unsplash.com/photo-1541745537411-b8046dc6d66c?w=400&h=250&fit=crop',
    recipeCount: 750
  },
  {
    id: '5',
    name: 'Desserts',
    slug: 'desserts',
    description: 'Sweet treats and desserts to satisfy your cravings',
    image: 'https://images.unsplash.com/photo-**********-0bccd828d307?w=400&h=250&fit=crop',
    recipeCount: 950
  },
  {
    id: '6',
    name: 'Salads',
    slug: 'salads',
    description: 'Fresh and healthy salad recipes',
    image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=250&fit=crop',
    recipeCount: 450
  },
  {
    id: '7',
    name: 'Soups & Stews',
    slug: 'soups-stews',
    description: 'Comforting soups and hearty stews',
    image: 'https://images.unsplash.com/photo-**********-85f173990554?w=400&h=250&fit=crop',
    recipeCount: 550
  },
  {
    id: '8',
    name: 'Side Dishes',
    slug: 'side-dishes',
    description: 'Perfect accompaniments to your main dishes',
    image: 'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=400&h=250&fit=crop',
    recipeCount: 400
  },
  {
    id: '9',
    name: 'Drinks',
    slug: 'drinks',
    description: 'Refreshing beverages and cocktails',
    image: 'https://images.unsplash.com/photo-**********-f90425340c7e?w=400&h=250&fit=crop',
    recipeCount: 300
  },
  {
    id: '10',
    name: 'Bread & Baking',
    slug: 'bread-baking',
    description: 'Homemade breads and baked goods',
    image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=250&fit=crop',
    recipeCount: 500
  }
];

export const cuisines = [
  { id: '1', name: 'Italian', slug: 'italian' },
  { id: '2', name: 'Mexican', slug: 'mexican' },
  { id: '3', name: 'Chinese', slug: 'chinese' },
  { id: '4', name: 'Indian', slug: 'indian' },
  { id: '5', name: 'French', slug: 'french' },
  { id: '6', name: 'Japanese', slug: 'japanese' },
  { id: '7', name: 'Thai', slug: 'thai' },
  { id: '8', name: 'Greek', slug: 'greek' },
  { id: '9', name: 'American', slug: 'american' },
  { id: '10', name: 'Mediterranean', slug: 'mediterranean' }
];
