'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Clock, Users, Star, Heart, Bookmark, Share2, ChefHat, Timer } from 'lucide-react';
import { featuredRecipes } from '@/data/recipes';
import { Recipe } from '@/types';

interface RecipePageProps {
  params: {
    id: string;
  };
}

export default function RecipePage({ params }: RecipePageProps) {
  const [activeTab, setActiveTab] = useState('ingredients');
  const [servings, setServings] = useState(4);
  
  // In a real app, you would fetch the recipe by ID
  const recipe = featuredRecipes.find(r => r.id === params.id) || featuredRecipes[0];
  
  const adjustIngredientAmount = (amount: string, originalServings: number, newServings: number) => {
    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount)) return amount;
    const adjusted = (numericAmount * newServings) / originalServings;
    return adjusted % 1 === 0 ? adjusted.toString() : adjusted.toFixed(1);
  };

  return (
    <div className="bg-white">
      {/* Recipe Header */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Recipe Image */}
          <div className="relative">
            <Image
              src={recipe.image}
              alt={recipe.title}
              width={600}
              height={400}
              className="w-full h-96 object-cover rounded-lg"
            />
            <div className="absolute top-4 right-4 flex space-x-2">
              <button className="p-2 bg-white/90 rounded-full hover:bg-white transition-colors">
                <Heart className="h-5 w-5 text-gray-600 hover:text-red-500" />
              </button>
              <button className="p-2 bg-white/90 rounded-full hover:bg-white transition-colors">
                <Bookmark className="h-5 w-5 text-gray-600 hover:text-blue-500" />
              </button>
              <button className="p-2 bg-white/90 rounded-full hover:bg-white transition-colors">
                <Share2 className="h-5 w-5 text-gray-600 hover:text-green-500" />
              </button>
            </div>
          </div>

          {/* Recipe Info */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <span className="px-3 py-1 bg-orange-100 text-orange-700 text-sm font-medium rounded-full">
                {recipe.category}
              </span>
              <span className="px-3 py-1 bg-blue-100 text-blue-700 text-sm font-medium rounded-full">
                {recipe.cuisine}
              </span>
            </div>

            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {recipe.title}
            </h1>

            <p className="text-lg text-gray-600 mb-6">
              {recipe.description}
            </p>

            {/* Rating and Reviews */}
            <div className="flex items-center space-x-4 mb-6">
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-5 w-5 ${
                      i < Math.floor(recipe.rating)
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
                <span className="font-semibold text-lg">{recipe.rating}</span>
              </div>
              <span className="text-gray-500">({recipe.reviewCount} reviews)</span>
            </div>

            {/* Recipe Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <Timer className="h-6 w-6 text-orange-600 mx-auto mb-2" />
                <div className="text-sm text-gray-600">Prep Time</div>
                <div className="font-semibold">{recipe.prepTime}m</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <Clock className="h-6 w-6 text-orange-600 mx-auto mb-2" />
                <div className="text-sm text-gray-600">Cook Time</div>
                <div className="font-semibold">{recipe.cookTime}m</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <Users className="h-6 w-6 text-orange-600 mx-auto mb-2" />
                <div className="text-sm text-gray-600">Servings</div>
                <div className="font-semibold">{recipe.servings}</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <ChefHat className="h-6 w-6 text-orange-600 mx-auto mb-2" />
                <div className="text-sm text-gray-600">Difficulty</div>
                <div className="font-semibold">{recipe.difficulty}</div>
              </div>
            </div>

            {/* Author */}
            <div className="flex items-center space-x-3 p-4 bg-gray-50 rounded-lg">
              <Image
                src={recipe.author.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'}
                alt={recipe.author.name}
                width={48}
                height={48}
                className="w-12 h-12 rounded-full"
              />
              <div>
                <div className="font-semibold text-gray-900">{recipe.author.name}</div>
                <div className="text-sm text-gray-600">{recipe.author.bio}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recipe Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Tabs */}
            <div className="border-b border-gray-200 mb-6">
              <nav className="flex space-x-8">
                {['ingredients', 'instructions', 'nutrition'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm capitalize ${
                      activeTab === tab
                        ? 'border-orange-500 text-orange-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab}
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            {activeTab === 'ingredients' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">Ingredients</h2>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600">Servings:</span>
                    <button
                      onClick={() => setServings(Math.max(1, servings - 1))}
                      className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center"
                    >
                      -
                    </button>
                    <span className="w-8 text-center font-semibold">{servings}</span>
                    <button
                      onClick={() => setServings(servings + 1)}
                      className="w-8 h-8 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center"
                    >
                      +
                    </button>
                  </div>
                </div>
                <ul className="space-y-3">
                  {recipe.ingredients.map((ingredient) => (
                    <li key={ingredient.id} className="flex items-center space-x-3">
                      <input type="checkbox" className="w-4 h-4 text-orange-600 rounded" />
                      <span className="text-gray-900">
                        <span className="font-medium">
                          {adjustIngredientAmount(ingredient.amount, recipe.servings, servings)} {ingredient.unit}
                        </span>{' '}
                        {ingredient.name}
                        {ingredient.notes && (
                          <span className="text-gray-500 italic"> ({ingredient.notes})</span>
                        )}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {activeTab === 'instructions' && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Instructions</h2>
                <ol className="space-y-6">
                  {recipe.instructions.map((instruction) => (
                    <li key={instruction.id} className="flex space-x-4">
                      <span className="flex-shrink-0 w-8 h-8 bg-orange-600 text-white rounded-full flex items-center justify-center font-semibold">
                        {instruction.step}
                      </span>
                      <div className="flex-1">
                        <p className="text-gray-900 leading-relaxed">{instruction.description}</p>
                      </div>
                    </li>
                  ))}
                </ol>
              </div>
            )}

            {activeTab === 'nutrition' && (
              <div>
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Nutrition Information</h2>
                <div className="bg-gray-50 rounded-lg p-6">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{recipe.nutrition.calories}</div>
                      <div className="text-sm text-gray-600">Calories</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{recipe.nutrition.protein}g</div>
                      <div className="text-sm text-gray-600">Protein</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{recipe.nutrition.carbs}g</div>
                      <div className="text-sm text-gray-600">Carbs</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{recipe.nutrition.fat}g</div>
                      <div className="text-sm text-gray-600">Fat</div>
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div className="text-center">
                        <div className="font-semibold">{recipe.nutrition.fiber}g</div>
                        <div className="text-gray-600">Fiber</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold">{recipe.nutrition.sugar}g</div>
                        <div className="text-gray-600">Sugar</div>
                      </div>
                      <div className="text-center">
                        <div className="font-semibold">{recipe.nutrition.sodium}mg</div>
                        <div className="text-gray-600">Sodium</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Tags */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="font-semibold text-gray-900 mb-4">Recipe Tags</h3>
              <div className="flex flex-wrap gap-2">
                {recipe.tags.map((tag) => (
                  <Link
                    key={tag}
                    href={`/search?tag=${tag}`}
                    className="px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full hover:bg-orange-200 transition-colors"
                  >
                    #{tag}
                  </Link>
                ))}
              </div>
            </div>

            {/* Print Recipe */}
            <button className="w-full bg-orange-600 hover:bg-orange-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors">
              Print Recipe
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
