import * as React from 'react';
import type { DebugInfo } from '../../../../shared/types';
import type { ErrorMessageType } from '../error-message/error-message';
import type { ErrorType } from '../error-type-label/error-type-label';
import type { ErrorBaseProps } from '../error-overlay/error-overlay';
import type { ReadyRuntimeError } from '../../../utils/get-error-by-type';
export interface ErrorOverlayLayoutProps extends ErrorBaseProps {
    errorMessage: ErrorMessageType;
    errorType: ErrorType;
    children?: React.ReactNode;
    errorCode?: string;
    error: ReadyRuntimeError['error'];
    debugInfo?: DebugInfo;
    isBuildError?: boolean;
    onClose?: () => void;
    runtimeErrors?: ReadyRuntimeError[];
    activeIdx?: number;
    setActiveIndex?: (index: number) => void;
    dialogResizerRef?: React.RefObject<HTMLDivElement | null>;
}
export declare function ErrorOverlayLayout({ errorMessage, errorType, children, errorCode, errorCount, error, debugInfo, isBuildError, onClose, versionInfo, runtimeErrors, activeIdx, setActiveIndex, isTurbopack, dialogResizerRef, rendered, transitionDurationMs, }: ErrorOverlayLayoutProps): import("react/jsx-runtime").JSX.Element;
export declare const styles = "\n  \n  [data-nextjs-dialog-overlay] {\n    padding: initial;\n    top: 10vh;\n  }\n\n  \n  .error-overlay-dialog-container {\n    -webkit-font-smoothing: antialiased;\n    display: flex;\n    flex-direction: column;\n    background: var(--color-background-100);\n    background-clip: padding-box;\n    border: var(--next-dialog-border-width) solid var(--color-gray-400);\n    border-radius: 0 0 var(--next-dialog-radius) var(--next-dialog-radius);\n    box-shadow: var(--shadow-menu);\n    position: relative;\n    overflow: hidden;\n  }\n\n  .error-overlay-dialog-scroll {\n    overflow-y: auto;\n    height: 100%;\n  }\n\n  \n  .nextjs-container-errors-header {\n    position: relative;\n  }\n  .nextjs-container-errors-header > h1 {\n    font-size: var(--size-20);\n    line-height: var(--size-24);\n    font-weight: bold;\n    margin: calc(16px * 1.5) 0;\n    color: var(--color-title-h1);\n  }\n  .nextjs-container-errors-header small {\n    font-size: var(--size-14);\n    color: var(--color-accents-1);\n    margin-left: 16px;\n  }\n  .nextjs-container-errors-header small > span {\n    font-family: var(--font-stack-monospace);\n  }\n  .nextjs-container-errors-header > div > small {\n    margin: 0;\n    margin-top: 4px;\n  }\n  .nextjs-container-errors-header > p > a {\n    color: inherit;\n    font-weight: bold;\n  }\n  .nextjs-container-errors-header\n    > .nextjs-container-build-error-version-status {\n    position: absolute;\n    top: 16px;\n    right: 16px;\n  }\n\n  \n\n  \n  [data-nextjs-error-overlay-nav] {\n    --stroke-color: var(--color-gray-400);\n    --background-color: var(--color-background-100);\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    width: 100%;\n\n    position: relative;\n    z-index: 2;\n    outline: none;\n    translate: var(--next-dialog-border-width) var(--next-dialog-border-width);\n    max-width: var(--next-dialog-max-width);\n\n    .error-overlay-notch {\n      translate: calc(var(--next-dialog-border-width) * -1);\n      width: auto;\n      height: var(--next-dialog-notch-height);\n      padding: 12px;\n      background: var(--background-color);\n      border: var(--next-dialog-border-width) solid var(--stroke-color);\n      border-bottom: none;\n      position: relative;\n\n      &[data-side='left'] {\n        padding-right: 0;\n        border-radius: var(--next-dialog-radius) 0 0 0;\n\n        .error-overlay-notch-tail {\n          right: -54px;\n        }\n\n        > *:not(.error-overlay-notch-tail) {\n          margin-right: -10px;\n        }\n      }\n\n      &[data-side='right'] {\n        padding-left: 0;\n        border-radius: 0 var(--next-dialog-radius) 0 0;\n\n        .error-overlay-notch-tail {\n          left: -54px;\n          transform: rotateY(180deg);\n        }\n\n        > *:not(.error-overlay-notch-tail) {\n          margin-left: -12px;\n        }\n      }\n\n      .error-overlay-notch-tail {\n        position: absolute;\n        top: calc(var(--next-dialog-border-width) * -1);\n        pointer-events: none;\n        z-index: -1;\n        height: calc(100% + var(--next-dialog-border-width));\n      }\n    }\n  }\n\n  @media (max-width: 600px) {\n    [data-nextjs-error-overlay-nav] {\n      background: var(--background-color);\n      border-radius: var(--next-dialog-radius) var(--next-dialog-radius) 0 0;\n      border: var(--next-dialog-border-width) solid var(--stroke-color);\n      border-bottom: none;\n      overflow: hidden;\n      translate: 0 var(--next-dialog-border-width);\n      \n      .error-overlay-notch {\n        border-radius: 0;\n        border: 0;\n\n        &[data-side=\"left\"], &[data-side=\"right\"] {\n          border-radius: 0;\n        }\n\n        .error-overlay-notch-tail {\n          display: none;\n        }\n      }\n    }\n  }\n\n  \n  .nextjs__container_errors_label {\n    padding: 2px 6px;\n    margin: 0;\n    border-radius: var(--rounded-md-2);\n    background: var(--color-red-100);\n    font-weight: 600;\n    font-size: var(--size-12);\n    color: var(--color-red-900);\n    font-family: var(--font-stack-monospace);\n    line-height: var(--size-20);\n  }\n\n  \n  .nextjs__container_errors_wrapper {\n    position: relative;\n  }\n\n  .nextjs__container_errors_desc {\n    margin: 0;\n    margin-left: 4px;\n    color: var(--color-red-900);\n    font-weight: 500;\n    font-size: var(--size-16);\n    letter-spacing: -0.32px;\n    line-height: var(--size-24);\n    overflow-wrap: break-word;\n    white-space: pre-wrap;\n  }\n\n  .nextjs__container_errors_desc.truncated {\n    max-height: 200px;\n    overflow: hidden;\n  }\n\n  .nextjs__container_errors_gradient_overlay {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    height: 85px;\n    background: linear-gradient(\n      180deg,\n      rgba(250, 250, 250, 0) 0%,\n      var(--color-background-100) 100%\n    );\n  }\n\n  .nextjs__container_errors_expand_button {\n    position: absolute;\n    bottom: 10px;\n    left: 50%;\n    transform: translateX(-50%);\n    display: flex;\n    align-items: center;\n    padding: 6px 8px;\n    background: var(--color-background-100);\n    border: 1px solid var(--color-gray-alpha-400);\n    border-radius: 999px;\n    box-shadow:\n      0px 2px 2px var(--color-gray-alpha-100),\n      0px 8px 8px -8px var(--color-gray-alpha-100);\n    font-size: var(--size-13);\n    cursor: pointer;\n    color: var(--color-gray-900);\n    font-weight: 500;\n    transition: background-color 0.2s ease;\n  }\n\n  .nextjs__container_errors_expand_button:hover {\n    background: var(--color-gray-100);\n  }\n\n  \n  .error-overlay-toolbar {\n    display: flex;\n    gap: 6px;\n  }\n\n  .nodejs-inspector-button,\n  .copy-stack-trace-button,\n  .docs-link-button {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n\n    width: var(--size-28);\n    height: var(--size-28);\n    background: var(--color-background-100);\n    background-clip: padding-box;\n    border: 1px solid var(--color-gray-alpha-400);\n    box-shadow: var(--shadow-small);\n    border-radius: var(--rounded-full);\n\n    svg {\n      width: var(--size-14);\n      height: var(--size-14);\n    }\n\n    &:focus {\n      outline: var(--focus-ring);\n    }\n\n    &:not(:disabled):hover {\n      background: var(--color-gray-alpha-100);\n    }\n\n    &:not(:disabled):active {\n      background: var(--color-gray-alpha-200);\n    }\n\n    &:disabled {\n      background-color: var(--color-gray-100);\n      cursor: not-allowed;\n    }\n  }\n\n  .error-overlay-toolbar-button-icon {\n    color: var(--color-gray-900);\n  }\n\n\n  [data-nextjs-error-label-group] {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n";
