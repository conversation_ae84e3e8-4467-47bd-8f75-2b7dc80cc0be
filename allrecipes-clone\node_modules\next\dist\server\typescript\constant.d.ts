export declare const NEXT_TS_ERRORS: {
    INVALID_SERVER_API: number;
    INVALID_ENTRY_EXPORT: number;
    INVALID_OPTION_VALUE: number;
    MISPLACED_ENTRY_DIRECTIVE: number;
    INVALID_PAGE_PROP: number;
    INVALID_CONFIG_OPTION: number;
    INVALID_CLIENT_ENTRY_PROP: number;
    INVALID_METADATA_EXPORT: number;
    INVALID_ERROR_COMPONENT: number;
    INVALID_ENTRY_DIRECTIVE: number;
    INVALID_SERVER_ENTRY_RETURN: number;
};
export declare const ALLOWED_EXPORTS: string[];
export declare const LEGACY_CONFIG_EXPORT = "config";
export declare const DISALLOWED_SERVER_REACT_APIS: string[];
export declare const DISALLOWED_SERVER_REACT_DOM_APIS: string[];
export declare const ALLOWED_PAGE_PROPS: string[];
export declare const ALLOWED_LAYOUT_PROPS: string[];
