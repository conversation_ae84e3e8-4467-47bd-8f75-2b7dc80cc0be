{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/food/allrecipes-clone/src/components/ui/RecipeCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Clock, Users, Star, Heart } from 'lucide-react';\nimport { Recipe } from '@/types';\n\ninterface RecipeCardProps {\n  recipe: Recipe;\n  className?: string;\n}\n\nconst RecipeCard: React.FC<RecipeCardProps> = ({ recipe, className = '' }) => {\n  return (\n    <div className={`bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 ${className}`}>\n      <Link href={`/recipe/${recipe.id}`}>\n        <div className=\"relative\">\n          <Image\n            src={recipe.image}\n            alt={recipe.title}\n            width={400}\n            height={250}\n            className=\"w-full h-48 object-cover\"\n          />\n          <button className=\"absolute top-3 right-3 p-2 bg-white/80 rounded-full hover:bg-white transition-colors duration-200\">\n            <Heart className=\"h-5 w-5 text-gray-600 hover:text-red-500\" />\n          </button>\n          <div className=\"absolute bottom-3 left-3 bg-black/70 text-white px-2 py-1 rounded text-sm\">\n            {recipe.difficulty}\n          </div>\n        </div>\n      </Link>\n\n      <div className=\"p-4\">\n        <Link href={`/recipe/${recipe.id}`}>\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2 hover:text-orange-600 transition-colors duration-200 line-clamp-2\">\n            {recipe.title}\n          </h3>\n        </Link>\n        \n        <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n          {recipe.description}\n        </p>\n\n        <div className=\"flex items-center justify-between text-sm text-gray-500 mb-3\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-1\">\n              <Clock className=\"h-4 w-4\" />\n              <span>{recipe.totalTime}m</span>\n            </div>\n            <div className=\"flex items-center space-x-1\">\n              <Users className=\"h-4 w-4\" />\n              <span>{recipe.servings}</span>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-1\">\n            <Star className=\"h-4 w-4 fill-yellow-400 text-yellow-400\" />\n            <span className=\"font-medium\">{recipe.rating}</span>\n            <span className=\"text-gray-400\">({recipe.reviewCount})</span>\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2\">\n            <Image\n              src={recipe.author.avatar || 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'}\n              alt={recipe.author.name}\n              width={24}\n              height={24}\n              className=\"w-6 h-6 rounded-full\"\n            />\n            <span className=\"text-sm text-gray-600\">{recipe.author.name}</span>\n          </div>\n          \n          <div className=\"flex flex-wrap gap-1\">\n            {recipe.tags.slice(0, 2).map((tag) => (\n              <span\n                key={tag}\n                className=\"px-2 py-1 bg-orange-100 text-orange-700 text-xs rounded-full\"\n              >\n                {tag}\n              </span>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RecipeCard;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;AAQA,MAAM,aAAwC,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE;IACvE,qBACE,8OAAC;QAAI,WAAW,CAAC,6FAA6F,EAAE,WAAW;;0BACzH,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE;0BAChC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,OAAO,KAAK;4BACjB,KAAK,OAAO,KAAK;4BACjB,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;sCAEZ,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,8OAAC;4BAAI,WAAU;sCACZ,OAAO,UAAU;;;;;;;;;;;;;;;;;0BAKxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE;kCAChC,cAAA,8OAAC;4BAAG,WAAU;sCACX,OAAO,KAAK;;;;;;;;;;;kCAIjB,8OAAC;wBAAE,WAAU;kCACV,OAAO,WAAW;;;;;;kCAGrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;oDAAM,OAAO,SAAS;oDAAC;;;;;;;;;;;;;kDAE1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAM,OAAO,QAAQ;;;;;;;;;;;;;;;;;;0CAI1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAe,OAAO,MAAM;;;;;;kDAC5C,8OAAC;wCAAK,WAAU;;4CAAgB;4CAAE,OAAO,WAAW;4CAAC;;;;;;;;;;;;;;;;;;;kCAIzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAK,OAAO,MAAM,CAAC,MAAM,IAAI;wCAC7B,KAAK,OAAO,MAAM,CAAC,IAAI;wCACvB,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEZ,8OAAC;wCAAK,WAAU;kDAAyB,OAAO,MAAM,CAAC,IAAI;;;;;;;;;;;;0CAG7D,8OAAC;gCAAI,WAAU;0CACZ,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBAC5B,8OAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrB;uCAEe", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/food/allrecipes-clone/src/components/ui/CategoryCard.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Category } from '@/types';\n\ninterface CategoryCardProps {\n  category: Category;\n  className?: string;\n}\n\nconst CategoryCard: React.FC<CategoryCardProps> = ({ category, className = '' }) => {\n  return (\n    <Link href={`/category/${category.slug}`}>\n      <div className={`relative group cursor-pointer overflow-hidden rounded-lg ${className}`}>\n        <div className=\"relative h-48 w-full\">\n          <Image\n            src={category.image}\n            alt={category.name}\n            fill\n            className=\"object-cover transition-transform duration-300 group-hover:scale-105\"\n          />\n          <div className=\"absolute inset-0 bg-black/40 group-hover:bg-black/50 transition-colors duration-300\" />\n        </div>\n        \n        <div className=\"absolute inset-0 flex flex-col justify-end p-4\">\n          <h3 className=\"text-white text-xl font-bold mb-1 group-hover:text-orange-300 transition-colors duration-300\">\n            {category.name}\n          </h3>\n          <p className=\"text-white/90 text-sm mb-2\">\n            {category.description}\n          </p>\n          <p className=\"text-orange-300 text-sm font-medium\">\n            {category.recipeCount.toLocaleString()} recipes\n          </p>\n        </div>\n      </div>\n    </Link>\n  );\n};\n\nexport default CategoryCard;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAQA,MAAM,eAA4C,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE;IAC7E,qBACE,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,UAAU,EAAE,SAAS,IAAI,EAAE;kBACtC,cAAA,8OAAC;YAAI,WAAW,CAAC,yDAAyD,EAAE,WAAW;;8BACrF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAK,SAAS,KAAK;4BACnB,KAAK,SAAS,IAAI;4BAClB,IAAI;4BACJ,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCACX,SAAS,IAAI;;;;;;sCAEhB,8OAAC;4BAAE,WAAU;sCACV,SAAS,WAAW;;;;;;sCAEvB,8OAAC;4BAAE,WAAU;;gCACV,SAAS,WAAW,CAAC,cAAc;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;AAMnD;uCAEe", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/food/allrecipes-clone/src/data/recipes.ts"], "sourcesContent": ["import { Recipe } from '@/types';\n\nexport const featuredRecipes: <PERSON>cipe[] = [\n  {\n    id: '1',\n    title: 'Classic Spaghetti Carbonara',\n    description: 'A traditional Italian pasta dish with eggs, cheese, pancetta, and black pepper',\n    image: 'https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=400&h=250&fit=crop',\n    prepTime: 15,\n    cookTime: 20,\n    totalTime: 35,\n    servings: 4,\n    difficulty: 'Medium',\n    rating: 4.8,\n    reviewCount: 1250,\n    ingredients: [\n      { id: '1', name: 'Spaghetti', amount: '400', unit: 'g', notes: '' },\n      { id: '2', name: 'Pancetta', amount: '150', unit: 'g', notes: 'diced' },\n      { id: '3', name: 'Large eggs', amount: '3', unit: 'whole', notes: '' },\n      { id: '4', name: 'Parmesan cheese', amount: '100', unit: 'g', notes: 'grated' },\n      { id: '5', name: 'Black pepper', amount: '1', unit: 'tsp', notes: 'freshly ground' },\n      { id: '6', name: 'Salt', amount: 'to taste', unit: '', notes: '' }\n    ],\n    instructions: [\n      { id: '1', step: 1, description: 'Bring a large pot of salted water to boil. Cook spaghetti according to package directions until al dente.' },\n      { id: '2', step: 2, description: 'While pasta cooks, heat a large skillet over medium heat. Add pancetta and cook until crispy, about 5-7 minutes.' },\n      { id: '3', step: 3, description: 'In a bowl, whisk together eggs, grated Parmesan, and black pepper.' },\n      { id: '4', step: 4, description: 'Drain pasta, reserving 1 cup of pasta water. Add hot pasta to the skillet with pancetta.' },\n      { id: '5', step: 5, description: 'Remove from heat and quickly stir in egg mixture, adding pasta water as needed to create a creamy sauce.' },\n      { id: '6', step: 6, description: 'Serve immediately with additional Parmesan and black pepper.' }\n    ],\n    nutrition: {\n      calories: 520,\n      protein: 22,\n      carbs: 65,\n      fat: 18,\n      fiber: 3,\n      sugar: 2,\n      sodium: 680\n    },\n    tags: ['pasta', 'italian', 'quick', 'comfort-food'],\n    category: 'Dinners',\n    cuisine: 'Italian',\n    author: {\n      id: '1',\n      name: 'Chef Marco',\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',\n      bio: 'Italian cuisine specialist with 15 years of experience'\n    },\n    createdAt: '2024-01-15T10:00:00Z',\n    updatedAt: '2024-01-15T10:00:00Z'\n  },\n  {\n    id: '2',\n    title: 'Chocolate Chip Cookies',\n    description: 'Soft and chewy chocolate chip cookies that are perfect for any occasion',\n    image: 'https://images.unsplash.com/photo-1499636136210-6f4ee915583e?w=400&h=250&fit=crop',\n    prepTime: 15,\n    cookTime: 12,\n    totalTime: 27,\n    servings: 24,\n    difficulty: 'Easy',\n    rating: 4.9,\n    reviewCount: 2100,\n    ingredients: [\n      { id: '1', name: 'All-purpose flour', amount: '2¼', unit: 'cups', notes: '' },\n      { id: '2', name: 'Baking soda', amount: '1', unit: 'tsp', notes: '' },\n      { id: '3', name: 'Salt', amount: '1', unit: 'tsp', notes: '' },\n      { id: '4', name: 'Butter', amount: '1', unit: 'cup', notes: 'softened' },\n      { id: '5', name: 'Granulated sugar', amount: '¾', unit: 'cup', notes: '' },\n      { id: '6', name: 'Brown sugar', amount: '¾', unit: 'cup', notes: 'packed' },\n      { id: '7', name: 'Large eggs', amount: '2', unit: 'whole', notes: '' },\n      { id: '8', name: 'Vanilla extract', amount: '2', unit: 'tsp', notes: '' },\n      { id: '9', name: 'Chocolate chips', amount: '2', unit: 'cups', notes: '' }\n    ],\n    instructions: [\n      { id: '1', step: 1, description: 'Preheat oven to 375°F (190°C).' },\n      { id: '2', step: 2, description: 'In a medium bowl, whisk together flour, baking soda, and salt.' },\n      { id: '3', step: 3, description: 'In a large bowl, cream together butter and both sugars until light and fluffy.' },\n      { id: '4', step: 4, description: 'Beat in eggs one at a time, then stir in vanilla.' },\n      { id: '5', step: 5, description: 'Gradually blend in flour mixture, then stir in chocolate chips.' },\n      { id: '6', step: 6, description: 'Drop rounded tablespoons of dough onto ungreased cookie sheets.' },\n      { id: '7', step: 7, description: 'Bake for 9-11 minutes or until golden brown. Cool on baking sheet for 2 minutes before removing.' }\n    ],\n    nutrition: {\n      calories: 180,\n      protein: 2,\n      carbs: 26,\n      fat: 8,\n      fiber: 1,\n      sugar: 18,\n      sodium: 140\n    },\n    tags: ['cookies', 'dessert', 'baking', 'chocolate'],\n    category: 'Desserts',\n    cuisine: 'American',\n    author: {\n      id: '2',\n      name: 'Baker Sarah',\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',\n      bio: 'Professional baker and dessert specialist'\n    },\n    createdAt: '2024-01-10T14:30:00Z',\n    updatedAt: '2024-01-10T14:30:00Z'\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,kBAA4B;IACvC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,aAAa;QACb,aAAa;YACX;gBAAE,IAAI;gBAAK,MAAM;gBAAa,QAAQ;gBAAO,MAAM;gBAAK,OAAO;YAAG;YAClE;gBAAE,IAAI;gBAAK,MAAM;gBAAY,QAAQ;gBAAO,MAAM;gBAAK,OAAO;YAAQ;YACtE;gBAAE,IAAI;gBAAK,MAAM;gBAAc,QAAQ;gBAAK,MAAM;gBAAS,OAAO;YAAG;YACrE;gBAAE,IAAI;gBAAK,MAAM;gBAAmB,QAAQ;gBAAO,MAAM;gBAAK,OAAO;YAAS;YAC9E;gBAAE,IAAI;gBAAK,MAAM;gBAAgB,QAAQ;gBAAK,MAAM;gBAAO,OAAO;YAAiB;YACnF;gBAAE,IAAI;gBAAK,MAAM;gBAAQ,QAAQ;gBAAY,MAAM;gBAAI,OAAO;YAAG;SAClE;QACD,cAAc;YACZ;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAA4G;YAC7I;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAAmH;YACpJ;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAAqE;YACtG;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAA2F;YAC5H;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAA2G;YAC5I;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAA+D;SACjG;QACD,WAAW;YACT,UAAU;YACV,SAAS;YACT,OAAO;YACP,KAAK;YACL,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QACA,MAAM;YAAC;YAAS;YAAW;YAAS;SAAe;QACnD,UAAU;QACV,SAAS;QACT,QAAQ;YACN,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QACA,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,UAAU;QACV,UAAU;QACV,WAAW;QACX,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,aAAa;QACb,aAAa;YACX;gBAAE,IAAI;gBAAK,MAAM;gBAAqB,QAAQ;gBAAM,MAAM;gBAAQ,OAAO;YAAG;YAC5E;gBAAE,IAAI;gBAAK,MAAM;gBAAe,QAAQ;gBAAK,MAAM;gBAAO,OAAO;YAAG;YACpE;gBAAE,IAAI;gBAAK,MAAM;gBAAQ,QAAQ;gBAAK,MAAM;gBAAO,OAAO;YAAG;YAC7D;gBAAE,IAAI;gBAAK,MAAM;gBAAU,QAAQ;gBAAK,MAAM;gBAAO,OAAO;YAAW;YACvE;gBAAE,IAAI;gBAAK,MAAM;gBAAoB,QAAQ;gBAAK,MAAM;gBAAO,OAAO;YAAG;YACzE;gBAAE,IAAI;gBAAK,MAAM;gBAAe,QAAQ;gBAAK,MAAM;gBAAO,OAAO;YAAS;YAC1E;gBAAE,IAAI;gBAAK,MAAM;gBAAc,QAAQ;gBAAK,MAAM;gBAAS,OAAO;YAAG;YACrE;gBAAE,IAAI;gBAAK,MAAM;gBAAmB,QAAQ;gBAAK,MAAM;gBAAO,OAAO;YAAG;YACxE;gBAAE,IAAI;gBAAK,MAAM;gBAAmB,QAAQ;gBAAK,MAAM;gBAAQ,OAAO;YAAG;SAC1E;QACD,cAAc;YACZ;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAAiC;YAClE;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAAiE;YAClG;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAAiF;YAClH;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAAoD;YACrF;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAAkE;YACnG;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAAkE;YACnG;gBAAE,IAAI;gBAAK,MAAM;gBAAG,aAAa;YAAmG;SACrI;QACD,WAAW;YACT,UAAU;YACV,SAAS;YACT,OAAO;YACP,KAAK;YACL,OAAO;YACP,OAAO;YACP,QAAQ;QACV;QACA,MAAM;YAAC;YAAW;YAAW;YAAU;SAAY;QACnD,UAAU;QACV,SAAS;QACT,QAAQ;YACN,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,KAAK;QACP;QACA,WAAW;QACX,WAAW;IACb;CACD", "debugId": null}}, {"offset": {"line": 641, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/food/allrecipes-clone/src/data/categories.ts"], "sourcesContent": ["import { Category } from '@/types';\n\nexport const categories: Category[] = [\n  {\n    id: '1',\n    name: 'Dinners',\n    slug: 'dinners',\n    description: 'Delicious dinner recipes for every occasion',\n    image: 'https://images.unsplash.com/photo-1546833999-b9f581a1996d?w=400&h=250&fit=crop',\n    recipeCount: 1250\n  },\n  {\n    id: '2',\n    name: 'Breakfast & Brunch',\n    slug: 'breakfast-brunch',\n    description: 'Start your day with amazing breakfast recipes',\n    image: 'https://images.unsplash.com/photo-1533089860892-a7c6f0a88666?w=400&h=250&fit=crop',\n    recipeCount: 850\n  },\n  {\n    id: '3',\n    name: 'Lunch',\n    slug: 'lunch',\n    description: 'Quick and satisfying lunch ideas',\n    image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=250&fit=crop',\n    recipeCount: 650\n  },\n  {\n    id: '4',\n    name: 'Appetizers & Snacks',\n    slug: 'appetizers-snacks',\n    description: 'Perfect starters and snacks for any gathering',\n    image: 'https://images.unsplash.com/photo-1541745537411-b8046dc6d66c?w=400&h=250&fit=crop',\n    recipeCount: 750\n  },\n  {\n    id: '5',\n    name: 'Desserts',\n    slug: 'desserts',\n    description: 'Sweet treats and desserts to satisfy your cravings',\n    image: 'https://images.unsplash.com/photo-**********-0bccd828d307?w=400&h=250&fit=crop',\n    recipeCount: 950\n  },\n  {\n    id: '6',\n    name: 'Salads',\n    slug: 'salads',\n    description: 'Fresh and healthy salad recipes',\n    image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=250&fit=crop',\n    recipeCount: 450\n  },\n  {\n    id: '7',\n    name: 'Soups & Stews',\n    slug: 'soups-stews',\n    description: 'Comforting soups and hearty stews',\n    image: 'https://images.unsplash.com/photo-**********-85f173990554?w=400&h=250&fit=crop',\n    recipeCount: 550\n  },\n  {\n    id: '8',\n    name: 'Side Dishes',\n    slug: 'side-dishes',\n    description: 'Perfect accompaniments to your main dishes',\n    image: 'https://images.unsplash.com/photo-1540420773420-3366772f4999?w=400&h=250&fit=crop',\n    recipeCount: 400\n  },\n  {\n    id: '9',\n    name: 'Drinks',\n    slug: 'drinks',\n    description: 'Refreshing beverages and cocktails',\n    image: 'https://images.unsplash.com/photo-**********-f90425340c7e?w=400&h=250&fit=crop',\n    recipeCount: 300\n  },\n  {\n    id: '10',\n    name: 'Bread & Baking',\n    slug: 'bread-baking',\n    description: 'Homemade breads and baked goods',\n    image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=250&fit=crop',\n    recipeCount: 500\n  }\n];\n\nexport const cuisines = [\n  { id: '1', name: 'Italian', slug: 'italian' },\n  { id: '2', name: 'Mexican', slug: 'mexican' },\n  { id: '3', name: 'Chinese', slug: 'chinese' },\n  { id: '4', name: 'Indian', slug: 'indian' },\n  { id: '5', name: 'French', slug: 'french' },\n  { id: '6', name: 'Japanese', slug: 'japanese' },\n  { id: '7', name: 'Thai', slug: 'thai' },\n  { id: '8', name: 'Greek', slug: 'greek' },\n  { id: '9', name: 'American', slug: 'american' },\n  { id: '10', name: 'Mediterranean', slug: 'mediterranean' }\n];\n"], "names": [], "mappings": ";;;;AAEO,MAAM,aAAyB;IACpC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,aAAa;IACf;CACD;AAEM,MAAM,WAAW;IACtB;QAAE,IAAI;QAAK,MAAM;QAAW,MAAM;IAAU;IAC5C;QAAE,IAAI;QAAK,MAAM;QAAW,MAAM;IAAU;IAC5C;QAAE,IAAI;QAAK,MAAM;QAAW,MAAM;IAAU;IAC5C;QAAE,IAAI;QAAK,MAAM;QAAU,MAAM;IAAS;IAC1C;QAAE,IAAI;QAAK,MAAM;QAAU,MAAM;IAAS;IAC1C;QAAE,IAAI;QAAK,MAAM;QAAY,MAAM;IAAW;IAC9C;QAAE,IAAI;QAAK,MAAM;QAAQ,MAAM;IAAO;IACtC;QAAE,IAAI;QAAK,MAAM;QAAS,MAAM;IAAQ;IACxC;QAAE,IAAI;QAAK,MAAM;QAAY,MAAM;IAAW;IAC9C;QAAE,IAAI;QAAM,MAAM;QAAiB,MAAM;IAAgB;CAC1D", "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/food/allrecipes-clone/src/app/page.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { Search, TrendingUp, Clock, Users, Star } from 'lucide-react';\nimport RecipeCard from '@/components/ui/RecipeCard';\nimport CategoryCard from '@/components/ui/CategoryCard';\nimport { featuredRecipes } from '@/data/recipes';\nimport { categories } from '@/data/categories';\n\nexport default function Home() {\n  const heroRecipes = featuredRecipes.slice(0, 3);\n  const trendingRecipes = featuredRecipes;\n  const popularCategories = categories.slice(0, 6);\n\n  return (\n    <div className=\"bg-white\">\n      {/* Hero Section */}\n      <section className=\"relative bg-gradient-to-r from-orange-500 to-red-500 text-white py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n              Find & Share Everyday\n              <br />\n              <span className=\"text-yellow-300\">Cooking Inspiration</span>\n            </h1>\n            <p className=\"text-xl md:text-2xl mb-8 text-orange-100\">\n              Discover thousands of recipes from home cooks like you\n            </p>\n\n            {/* Search Bar */}\n            <div className=\"max-w-2xl mx-auto\">\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search for recipes, ingredients, or cuisines...\"\n                  className=\"w-full px-6 py-4 pl-12 text-gray-900 rounded-full text-lg focus:outline-none focus:ring-4 focus:ring-orange-300\"\n                />\n                <Search className=\"absolute left-4 top-4 h-6 w-6 text-gray-400\" />\n                <button className=\"absolute right-2 top-2 bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-full font-semibold transition-colors duration-200\">\n                  Search\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"bg-gray-900 text-white py-12\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\">\n            <div>\n              <div className=\"text-4xl font-bold text-orange-400 mb-2\">51K+</div>\n              <div className=\"text-gray-300\">Original Recipes</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-orange-400 mb-2\">7M+</div>\n              <div className=\"text-gray-300\">Ratings & Reviews</div>\n            </div>\n            <div>\n              <div className=\"text-4xl font-bold text-orange-400 mb-2\">67M</div>\n              <div className=\"text-gray-300\">Home Cooks</div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Recipes */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Featured Recipes\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              Hand-picked recipes that our community loves\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {heroRecipes.map((recipe) => (\n              <RecipeCard key={recipe.id} recipe={recipe} />\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Popular Categories */}\n      <section className=\"py-16\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">\n              Popular Categories\n            </h2>\n            <p className=\"text-xl text-gray-600\">\n              Explore recipes by your favorite meal types\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {popularCategories.map((category) => (\n              <CategoryCard key={category.id} category={category} />\n            ))}\n          </div>\n\n          <div className=\"text-center mt-8\">\n            <Link\n              href=\"/categories\"\n              className=\"inline-flex items-center px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white font-semibold rounded-lg transition-colors duration-200\"\n            >\n              View All Categories\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Trending Now */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between mb-12\">\n            <div>\n              <h2 className=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4 flex items-center\">\n                <TrendingUp className=\"h-8 w-8 text-orange-600 mr-3\" />\n                Trending Now\n              </h2>\n              <p className=\"text-xl text-gray-600\">\n                What everyone's cooking this week\n              </p>\n            </div>\n            <Link\n              href=\"/trending\"\n              className=\"text-orange-600 hover:text-orange-700 font-semibold\"\n            >\n              See All →\n            </Link>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {trendingRecipes.map((recipe) => (\n              <RecipeCard key={recipe.id} recipe={recipe} />\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Newsletter CTA */}\n      <section className=\"py-16 bg-orange-600\">\n        <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n            Never Miss a Recipe\n          </h2>\n          <p className=\"text-xl text-orange-100 mb-8\">\n            Get the latest recipes, cooking tips, and kitchen inspiration delivered to your inbox.\n          </p>\n          <div className=\"max-w-md mx-auto flex\">\n            <input\n              type=\"email\"\n              placeholder=\"Enter your email\"\n              className=\"flex-1 px-4 py-3 rounded-l-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-300\"\n            />\n            <button className=\"bg-orange-700 hover:bg-orange-800 px-6 py-3 rounded-r-lg text-white font-semibold transition-colors duration-200\">\n              Subscribe\n            </button>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;IACtB,MAAM,cAAc,sHAAA,CAAA,kBAAe,CAAC,KAAK,CAAC,GAAG;IAC7C,MAAM,kBAAkB,sHAAA,CAAA,kBAAe;IACvC,MAAM,oBAAoB,yHAAA,CAAA,aAAU,CAAC,KAAK,CAAC,GAAG;IAE9C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAsC;kDAElD,8OAAC;;;;;kDACD,8OAAC;wCAAK,WAAU;kDAAkB;;;;;;;;;;;;0CAEpC,8OAAC;gCAAE,WAAU;0CAA2C;;;;;;0CAKxD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;sDAEZ,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAO,WAAU;sDAA0I;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUtK,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,sIAAA,CAAA,UAAU;oCAAiB,QAAQ;mCAAnB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;0BAOlC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAGlE,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,yBACtB,8OAAC,wIAAA,CAAA,UAAY;oCAAmB,UAAU;mCAAvB,SAAS,EAAE;;;;;;;;;;sCAIlC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiC;;;;;;;sDAGzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAKH,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC,sIAAA,CAAA,UAAU;oCAAiB,QAAQ;mCAAnB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;0BAOlC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAA+B;;;;;;sCAG5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;8CAEZ,8OAAC;oCAAO,WAAU;8CAAmH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjJ", "debugId": null}}]}