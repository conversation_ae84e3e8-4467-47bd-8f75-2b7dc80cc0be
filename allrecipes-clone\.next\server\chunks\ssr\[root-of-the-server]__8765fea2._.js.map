{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_9e72d27f-module__JKMi0a__className\",\n  \"variable\": \"inter_9e72d27f-module__JKMi0a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/food/allrecipes-clone/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/food/allrecipes-clone/src/components/layout/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/food/allrecipes-clone/src/components/layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Facebook, Instagram, Pinterest, Twitter, Youtube } from 'lucide-react';\n\nconst Footer = () => {\n  const footerSections = [\n    {\n      title: 'Recipes',\n      links: [\n        { name: 'Dinner Recipes', href: '/dinners' },\n        { name: 'Breakfast Recipes', href: '/breakfast' },\n        { name: 'Lunch Recipes', href: '/lunch' },\n        { name: 'Dessert Recipes', href: '/desserts' },\n        { name: 'Appetizer Recipes', href: '/appetizers' },\n        { name: 'Salad Recipes', href: '/salads' }\n      ]\n    },\n    {\n      title: 'Cuisines',\n      links: [\n        { name: 'Italian Recipes', href: '/cuisines/italian' },\n        { name: 'Mexican Recipes', href: '/cuisines/mexican' },\n        { name: 'Chinese Recipes', href: '/cuisines/chinese' },\n        { name: 'Indian Recipes', href: '/cuisines/indian' },\n        { name: 'French Recipes', href: '/cuisines/french' },\n        { name: 'Japanese Recipes', href: '/cuisines/japanese' }\n      ]\n    },\n    {\n      title: 'Kitchen Tips',\n      links: [\n        { name: 'Cooking Techniques', href: '/kitchen-tips/techniques' },\n        { name: 'Equipment Reviews', href: '/kitchen-tips/equipment' },\n        { name: 'Ingredient Guides', href: '/kitchen-tips/ingredients' },\n        { name: 'Meal Planning', href: '/kitchen-tips/meal-planning' },\n        { name: 'Food Safety', href: '/kitchen-tips/food-safety' },\n        { name: 'Substitutions', href: '/kitchen-tips/substitutions' }\n      ]\n    },\n    {\n      title: 'About',\n      links: [\n        { name: 'About Us', href: '/about' },\n        { name: 'Contact Us', href: '/contact' },\n        { name: 'Privacy Policy', href: '/privacy' },\n        { name: 'Terms of Service', href: '/terms' },\n        { name: 'Editorial Guidelines', href: '/editorial' },\n        { name: 'Careers', href: '/careers' }\n      ]\n    }\n  ];\n\n  const socialLinks = [\n    { name: 'Facebook', icon: Facebook, href: 'https://facebook.com/allrecipes' },\n    { name: 'Instagram', icon: Instagram, href: 'https://instagram.com/allrecipes' },\n    { name: 'Pinterest', icon: Pinterest, href: 'https://pinterest.com/allrecipes' },\n    { name: 'Twitter', icon: Twitter, href: 'https://twitter.com/allrecipes' },\n    { name: 'YouTube', icon: Youtube, href: 'https://youtube.com/allrecipes' }\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Newsletter Signup */}\n      <div className=\"bg-orange-600 py-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <h3 className=\"text-2xl font-bold mb-2\">Get Our Newsletter</h3>\n            <p className=\"text-orange-100 mb-6\">\n              Subscribe to get the latest recipes, cooking tips, and kitchen inspiration delivered to your inbox.\n            </p>\n            <div className=\"max-w-md mx-auto flex\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email address\"\n                className=\"flex-1 px-4 py-3 rounded-l-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-300\"\n              />\n              <button className=\"bg-orange-700 hover:bg-orange-800 px-6 py-3 rounded-r-lg font-semibold transition-colors duration-200\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Footer Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {footerSections.map((section) => (\n            <div key={section.title}>\n              <h4 className=\"text-lg font-semibold mb-4 text-orange-400\">{section.title}</h4>\n              <ul className=\"space-y-2\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <Link\n                      href={link.href}\n                      className=\"text-gray-300 hover:text-white transition-colors duration-200\"\n                    >\n                      {link.name}\n                    </Link>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n\n        {/* Social Media Links */}\n        <div className=\"border-t border-gray-700 mt-12 pt-8\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <div className=\"mb-4 md:mb-0\">\n              <Link href=\"/\" className=\"text-2xl font-bold text-orange-400\">\n                AllRecipes\n              </Link>\n              <p className=\"text-gray-400 mt-2\">\n                America's #1 Trusted Recipe Resource since 1997\n              </p>\n            </div>\n            \n            <div className=\"flex space-x-6\">\n              {socialLinks.map((social) => {\n                const IconComponent = social.icon;\n                return (\n                  <a\n                    key={social.name}\n                    href={social.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                    aria-label={social.name}\n                  >\n                    <IconComponent className=\"h-6 w-6\" />\n                  </a>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-gray-700 mt-8 pt-8 text-center text-gray-400 text-sm\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p>&copy; 2024 AllRecipes. All rights reserved.</p>\n            <div className=\"flex space-x-6 mt-4 md:mt-0\">\n              <Link href=\"/privacy\" className=\"hover:text-white transition-colors duration-200\">\n                Privacy Policy\n              </Link>\n              <Link href=\"/terms\" className=\"hover:text-white transition-colors duration-200\">\n                Terms of Service\n              </Link>\n              <Link href=\"/cookies\" className=\"hover:text-white transition-colors duration-200\">\n                Cookie Policy\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,SAAS;IACb,MAAM,iBAAiB;QACrB;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAkB,MAAM;gBAAW;gBAC3C;oBAAE,MAAM;oBAAqB,MAAM;gBAAa;gBAChD;oBAAE,MAAM;oBAAiB,MAAM;gBAAS;gBACxC;oBAAE,MAAM;oBAAmB,MAAM;gBAAY;gBAC7C;oBAAE,MAAM;oBAAqB,MAAM;gBAAc;gBACjD;oBAAE,MAAM;oBAAiB,MAAM;gBAAU;aAC1C;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAmB,MAAM;gBAAoB;gBACrD;oBAAE,MAAM;oBAAmB,MAAM;gBAAoB;gBACrD;oBAAE,MAAM;oBAAmB,MAAM;gBAAoB;gBACrD;oBAAE,MAAM;oBAAkB,MAAM;gBAAmB;gBACnD;oBAAE,MAAM;oBAAkB,MAAM;gBAAmB;gBACnD;oBAAE,MAAM;oBAAoB,MAAM;gBAAqB;aACxD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAsB,MAAM;gBAA2B;gBAC/D;oBAAE,MAAM;oBAAqB,MAAM;gBAA0B;gBAC7D;oBAAE,MAAM;oBAAqB,MAAM;gBAA4B;gBAC/D;oBAAE,MAAM;oBAAiB,MAAM;gBAA8B;gBAC7D;oBAAE,MAAM;oBAAe,MAAM;gBAA4B;gBACzD;oBAAE,MAAM;oBAAiB,MAAM;gBAA8B;aAC9D;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,MAAM;oBAAY,MAAM;gBAAS;gBACnC;oBAAE,MAAM;oBAAc,MAAM;gBAAW;gBACvC;oBAAE,MAAM;oBAAkB,MAAM;gBAAW;gBAC3C;oBAAE,MAAM;oBAAoB,MAAM;gBAAS;gBAC3C;oBAAE,MAAM;oBAAwB,MAAM;gBAAa;gBACnD;oBAAE,MAAM;oBAAW,MAAM;gBAAW;aACrC;QACH;KACD;IAED,MAAM,cAAc;QAClB;YAAE,MAAM;YAAY,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;QAAkC;QAC5E;YAAE,MAAM;YAAa,MAAM,4MAAA,CAAA,YAAS;YAAE,MAAM;QAAmC;QAC/E;YAAE,MAAM;YAAa,MAAM,kLAAA,CAAA,YAAS;YAAE,MAAM;QAAmC;QAC/E;YAAE,MAAM;YAAW,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;QAAiC;QACzE;YAAE,MAAM;YAAW,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;QAAiC;KAC1E;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CACxC,8OAAC;gCAAE,WAAU;0CAAuB;;;;;;0CAGpC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCAAO,WAAU;kDAAwG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlI,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA8C,QAAQ,KAAK;;;;;;kDACzE,8OAAC;wCAAG,WAAU;kDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,qBAClB,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAET,KAAK,IAAI;;;;;;+CALL,KAAK,IAAI;;;;;;;;;;;+BAJd,QAAQ,KAAK;;;;;;;;;;kCAmB3B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAqC;;;;;;sDAG9D,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;;8CAKpC,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC;wCAChB,MAAM,gBAAgB,OAAO,IAAI;wCACjC,qBACE,8OAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAY,OAAO,IAAI;sDAEvB,cAAA,8OAAC;gDAAc,WAAU;;;;;;2CAPpB,OAAO,IAAI;;;;;oCAUtB;;;;;;;;;;;;;;;;;kCAMN,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAE;;;;;;8CACH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAkD;;;;;;sDAGlF,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAAkD;;;;;;sDAGhF,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAkD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShG;uCAEe", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/food/allrecipes-clone/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport \"./globals.css\";\nimport Header from \"@/components/layout/Header\";\nimport Footer from \"@/components/layout/Footer\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n});\n\nexport const metadata: Metadata = {\n  title: \"AllRecipes - Find and Share Everyday Cooking Inspiration\",\n  description: \"Discover thousands of recipes, cooking tips, and kitchen inspiration from home cooks around the world.\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body className={`${inter.variable} font-sans antialiased bg-gray-50`}>\n        <Header />\n        <main className=\"min-h-screen\">\n          {children}\n        </main>\n        <Footer />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;;;;;;AAOO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,iCAAiC,CAAC;;8BACnE,8OAAC,sIAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACb;;;;;;8BAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}