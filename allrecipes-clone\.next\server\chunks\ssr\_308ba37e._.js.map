{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/food/allrecipes-clone/src/app/not-found.tsx"], "sourcesContent": ["import React from 'react';\nimport Link from 'next/link';\nimport { Home, Search, ChefHat } from 'lucide-react';\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full text-center\">\n        {/* 404 Illustration */}\n        <div className=\"mb-8\">\n          <div className=\"text-9xl font-bold text-orange-200 mb-4\">404</div>\n          <ChefHat className=\"h-16 w-16 text-orange-400 mx-auto\" />\n        </div>\n\n        {/* Error Message */}\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n          Recipe Not Found\n        </h1>\n        <p className=\"text-lg text-gray-600 mb-8\">\n          Oops! It looks like this recipe has gone missing from our kitchen. \n          Don't worry, we have plenty of other delicious recipes waiting for you!\n        </p>\n\n        {/* Action Buttons */}\n        <div className=\"space-y-4\">\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center justify-center w-full px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white font-semibold rounded-lg transition-colors duration-200\"\n          >\n            <Home className=\"h-5 w-5 mr-2\" />\n            Back to Home\n          </Link>\n          \n          <Link\n            href=\"/search\"\n            className=\"inline-flex items-center justify-center w-full px-6 py-3 border border-orange-600 text-orange-600 hover:bg-orange-50 font-semibold rounded-lg transition-colors duration-200\"\n          >\n            <Search className=\"h-5 w-5 mr-2\" />\n            Search Recipes\n          </Link>\n        </div>\n\n        {/* Popular Links */}\n        <div className=\"mt-12\">\n          <h3 className=\"text-sm font-medium text-gray-500 mb-4\">\n            Or try these popular categories:\n          </h3>\n          <div className=\"flex flex-wrap justify-center gap-2\">\n            {[\n              { name: 'Dinners', href: '/category/dinners' },\n              { name: 'Desserts', href: '/category/desserts' },\n              { name: 'Quick & Easy', href: '/search?q=quick' },\n              { name: 'Healthy', href: '/search?q=healthy' }\n            ].map((link) => (\n              <Link\n                key={link.name}\n                href={link.href}\n                className=\"px-3 py-1 bg-gray-100 hover:bg-orange-100 text-gray-700 hover:text-orange-700 text-sm rounded-full transition-colors duration-200\"\n              >\n                {link.name}\n              </Link>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAA0C;;;;;;sCACzD,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;;8BAIrB,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAGtD,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAM1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,mMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAInC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAMvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,MAAM;oCAAW,MAAM;gCAAoB;gCAC7C;oCAAE,MAAM;oCAAY,MAAM;gCAAqB;gCAC/C;oCAAE,MAAM;oCAAgB,MAAM;gCAAkB;gCAChD;oCAAE,MAAM;oCAAW,MAAM;gCAAoB;6BAC9C,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY9B", "debugId": null}}]}