import React from 'react';
import Link from 'next/link';
import { Home, Search, ChefHat } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full text-center">
        {/* 404 Illustration */}
        <div className="mb-8">
          <div className="text-9xl font-bold text-orange-200 mb-4">404</div>
          <ChefHat className="h-16 w-16 text-orange-400 mx-auto" />
        </div>

        {/* Error Message */}
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Recipe Not Found
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          Oops! It looks like this recipe has gone missing from our kitchen. 
          Don't worry, we have plenty of other delicious recipes waiting for you!
        </p>

        {/* Action Buttons */}
        <div className="space-y-4">
          <Link
            href="/"
            className="inline-flex items-center justify-center w-full px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white font-semibold rounded-lg transition-colors duration-200"
          >
            <Home className="h-5 w-5 mr-2" />
            Back to Home
          </Link>
          
          <Link
            href="/search"
            className="inline-flex items-center justify-center w-full px-6 py-3 border border-orange-600 text-orange-600 hover:bg-orange-50 font-semibold rounded-lg transition-colors duration-200"
          >
            <Search className="h-5 w-5 mr-2" />
            Search Recipes
          </Link>
        </div>

        {/* Popular Links */}
        <div className="mt-12">
          <h3 className="text-sm font-medium text-gray-500 mb-4">
            Or try these popular categories:
          </h3>
          <div className="flex flex-wrap justify-center gap-2">
            {[
              { name: 'Dinners', href: '/category/dinners' },
              { name: 'Desserts', href: '/category/desserts' },
              { name: 'Quick & Easy', href: '/search?q=quick' },
              { name: 'Healthy', href: '/search?q=healthy' }
            ].map((link) => (
              <Link
                key={link.name}
                href={link.href}
                className="px-3 py-1 bg-gray-100 hover:bg-orange-100 text-gray-700 hover:text-orange-700 text-sm rounded-full transition-colors duration-200"
              >
                {link.name}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
