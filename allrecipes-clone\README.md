# AllRecipes Clone - Website Công Thức Nấu Ăn

Một website clone của AllRecipes.com được xây dựng với Next.js, TypeScript và Tailwind CSS.

## 🌟 Tính năng

### 🏠 Trang chủ
- Hero section với thanh tìm kiếm
- Featured recipes (công thức nổi bật)
- Popular categories (danh mục phổ biến)
- Trending recipes (công thức thịnh hành)
- Newsletter signup

### 🍳 Công thức nấu ăn
- Trang chi tiết công thức với ingredients, instructions, nutrition
- Recipe cards với ratings, thời gian, độ khó
- Điều chỉnh số lượng phần ăn tương tác
- Categories và tags

### 🔍 Tìm kiếm & Lọc
- Tìm kiếm nâng cao
- Bộ lọc đa dạng (category, cuisine, difficulty, time, rating)
- Trang kết quả tìm kiếm với grid/list view

### 📱 Responsive Design
- Hoàn toàn responsive cho mobile, tablet, desktop
- Navigation menu responsive với dropdown
- Mobile-friendly hamburger menu

### 📂 Danh mục
- Trang danh sách categories
- Trang category riêng với filtering
- Category cards đẹp mắt

## 🛠️ Tech Stack

- **Next.js 15** - React framework với App Router
- **TypeScript** - Type safety
- **Tailwind CSS** - Utility-first CSS framework
- **Lucide React** - Beautiful icons
- **React Hooks** - State management

## 🚀 Cài đặt và Chạy

### Yêu cầu
- Node.js 18+
- npm hoặc yarn

### Cài đặt
```bash
# Clone repository
git clone <repository-url>
cd allrecipes-clone

# Cài đặt dependencies
npm install

# Chạy development server
npm run dev
```

### Scripts có sẵn
```bash
# Development server
npm run dev

# Build production
npm run build

# Start production server
npm start

# Lint code
npm run lint
```

## 📁 Cấu trúc Project

```
src/
├── app/                    # Next.js App Router
│   ├── about/             # Trang About
│   ├── categories/        # Trang danh sách categories
│   ├── category/[slug]/   # Trang category chi tiết
│   ├── recipe/[id]/       # Trang recipe chi tiết
│   ├── search/            # Trang tìm kiếm
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Homepage
│   ├── loading.tsx        # Loading component
│   └── not-found.tsx      # 404 page
├── components/            # React components
│   ├── layout/           # Layout components
│   │   ├── Header.tsx    # Header với navigation
│   │   └── Footer.tsx    # Footer
│   └── ui/               # UI components
│       ├── RecipeCard.tsx    # Recipe card component
│       └── CategoryCard.tsx  # Category card component
├── data/                 # Mock data
│   ├── recipes.ts        # Sample recipes
│   └── categories.ts     # Categories và cuisines
├── types/                # TypeScript types
│   └── index.ts          # Type definitions
└── utils/                # Utility functions
```

## 🎨 Design Features

### Color Scheme
- Primary: Orange (#EA580C)
- Secondary: Red (#DC2626)
- Neutral: Gray shades
- Accent: Yellow (#FCD34D)

### Typography
- Font: Inter (Google Fonts)
- Responsive font sizes
- Clear hierarchy

### Components
- Modern card designs
- Hover effects và transitions
- Loading states
- Error handling

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px
- **Large Desktop**: > 1280px

## 🔗 Navigation Structure

```
Home
├── Dinners
│   ├── 5-Ingredient Dinners
│   ├── One-Pot Meals
│   ├── Quick & Easy
│   └── ...
├── Meals
│   ├── Breakfast & Brunch
│   ├── Lunch
│   ├── Healthy
│   └── ...
├── Ingredients
├── Occasions
├── Cuisines
└── Kitchen Tips
```

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel
```

### Netlify
```bash
# Build
npm run build

# Deploy dist folder
```

## 🤝 Contributing

1. Fork the project
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Design inspiration from AllRecipes.com
- Images from Unsplash
- Icons from Lucide React
- Built with Next.js và Tailwind CSS

---

**Website đang chạy tại:** http://localhost:3001

Enjoy cooking! 👨‍🍳👩‍🍳
