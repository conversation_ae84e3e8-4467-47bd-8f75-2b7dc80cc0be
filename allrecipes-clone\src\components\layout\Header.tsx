'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { Search, Menu, X, User, Heart, Plus } from 'lucide-react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const navigationItems = [
    {
      name: 'Dinners',
      href: '/dinners',
      submenu: [
        { name: '5-Ingredient Dinners', href: '/dinners/5-ingredient' },
        { name: 'One-Pot Meals', href: '/dinners/one-pot' },
        { name: 'Quick & Easy', href: '/dinners/quick-easy' },
        { name: '30-Minute Meals', href: '/dinners/30-minute' },
        { name: 'Family Dinners', href: '/dinners/family' },
        { name: 'Comfort Food', href: '/dinners/comfort-food' }
      ]
    },
    {
      name: 'Me<PERSON>',
      href: '/meals',
      submenu: [
        { name: 'Breakfast & Brunch', href: '/meals/breakfast-brunch' },
        { name: 'Lunch', href: '/meals/lunch' },
        { name: 'Healthy', href: '/meals/healthy' },
        { name: 'Appetizers & Snacks', href: '/meals/appetizers-snacks' },
        { name: 'Salads', href: '/meals/salads' },
        { name: 'Side Dishes', href: '/meals/side-dishes' },
        { name: 'Soups', href: '/meals/soups' },
        { name: 'Desserts', href: '/meals/desserts' }
      ]
    },
    {
      name: 'Ingredients',
      href: '/ingredients',
      submenu: [
        { name: 'Chicken', href: '/ingredients/chicken' },
        { name: 'Beef', href: '/ingredients/beef' },
        { name: 'Pork', href: '/ingredients/pork' },
        { name: 'Seafood', href: '/ingredients/seafood' },
        { name: 'Pasta', href: '/ingredients/pasta' },
        { name: 'Vegetables', href: '/ingredients/vegetables' }
      ]
    },
    {
      name: 'Occasions',
      href: '/occasions',
      submenu: [
        { name: 'Summer Recipes', href: '/occasions/summer' },
        { name: 'Holiday Recipes', href: '/occasions/holidays' },
        { name: 'Party Food', href: '/occasions/party' },
        { name: 'Quick Weeknight', href: '/occasions/weeknight' }
      ]
    },
    {
      name: 'Cuisines',
      href: '/cuisines',
      submenu: [
        { name: 'Italian', href: '/cuisines/italian' },
        { name: 'Mexican', href: '/cuisines/mexican' },
        { name: 'Chinese', href: '/cuisines/chinese' },
        { name: 'Indian', href: '/cuisines/indian' },
        { name: 'French', href: '/cuisines/french' },
        { name: 'Japanese', href: '/cuisines/japanese' }
      ]
    },
    {
      name: 'Kitchen Tips',
      href: '/kitchen-tips',
      submenu: [
        { name: 'Cooking Techniques', href: '/kitchen-tips/techniques' },
        { name: 'Equipment Reviews', href: '/kitchen-tips/equipment' },
        { name: 'Ingredient Substitutions', href: '/kitchen-tips/substitutions' },
        { name: 'Meal Planning', href: '/kitchen-tips/meal-planning' }
      ]
    }
  ];

  return (
    <header className="bg-white shadow-md sticky top-0 z-50">
      {/* Top Bar */}
      <div className="bg-orange-500 text-white py-2">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center text-sm">
            <div className="flex space-x-4">
              <Link href="/newsletter" className="hover:underline">Newsletter</Link>
              <Link href="/magazine" className="hover:underline">Magazine</Link>
            </div>
            <div className="flex space-x-4">
              <Link href="/login" className="hover:underline">Log In</Link>
              <Link href="/signup" className="hover:underline">Sign Up</Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <span className="text-2xl font-bold text-orange-600">AllRecipes</span>
          </Link>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-2xl mx-8">
            <div className="relative w-full">
              <input
                type="text"
                placeholder="Find a recipe or ingredient..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 pl-10 pr-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
          </div>

          {/* User Actions */}
          <div className="flex items-center space-x-4">
            <button className="hidden md:flex items-center space-x-1 text-gray-700 hover:text-orange-600">
              <Plus className="h-5 w-5" />
              <span>Add Recipe</span>
            </button>
            <button className="text-gray-700 hover:text-orange-600">
              <Heart className="h-6 w-6" />
            </button>
            <button className="text-gray-700 hover:text-orange-600">
              <User className="h-6 w-6" />
            </button>
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden text-gray-700 hover:text-orange-600"
            >
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>

        {/* Navigation - Desktop */}
        <nav className="hidden lg:block border-t border-gray-200">
          <div className="flex space-x-6 xl:space-x-8 py-4">
            {navigationItems.map((item) => (
              <div key={item.name} className="relative group">
                <Link
                  href={item.href}
                  className="text-gray-700 hover:text-orange-600 font-medium transition-colors duration-200 text-sm xl:text-base"
                >
                  {item.name}
                </Link>
                {/* Dropdown Menu */}
                <div className="absolute left-0 mt-2 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                  <div className="py-2">
                    {item.submenu.map((subItem) => (
                      <Link
                        key={subItem.name}
                        href={subItem.href}
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-600"
                      >
                        {subItem.name}
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </nav>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white border-t border-gray-200">
          {/* Mobile Search */}
          <div className="px-4 py-3 border-b border-gray-200">
            <div className="relative">
              <input
                type="text"
                placeholder="Find a recipe..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>
          </div>

          {/* Mobile Navigation */}
          <div className="py-2">
            {navigationItems.map((item) => (
              <div key={item.name}>
                <Link
                  href={item.href}
                  className="block px-4 py-3 text-gray-700 hover:bg-orange-50 hover:text-orange-600 font-medium"
                >
                  {item.name}
                </Link>
                <div className="pl-8">
                  {item.submenu.slice(0, 4).map((subItem) => (
                    <Link
                      key={subItem.name}
                      href={subItem.href}
                      className="block px-4 py-2 text-sm text-gray-600 hover:bg-orange-50 hover:text-orange-600"
                    >
                      {subItem.name}
                    </Link>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
