import React from 'react';
import CategoryCard from '@/components/ui/CategoryCard';
import { categories } from '@/data/categories';

export default function CategoriesPage() {
  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-orange-500 to-red-500 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Recipe Categories
            </h1>
            <p className="text-xl text-orange-100">
              Discover recipes organized by meal type, cuisine, and cooking style
            </p>
          </div>
        </div>
      </section>

      {/* Categories Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {categories.map((category) => (
              <CategoryCard key={category.id} category={category} />
            ))}
          </div>
        </div>
      </section>

      {/* Popular Searches */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Popular Recipe Searches
          </h2>
          <div className="flex flex-wrap justify-center gap-3">
            {[
              'Chicken Recipes', 'Pasta Recipes', 'Vegetarian', 'Quick & Easy',
              'Healthy Recipes', 'Desserts', 'Soup Recipes', 'Salad Recipes',
              'Breakfast Ideas', 'Dinner Recipes', 'Appetizers', 'Slow Cooker'
            ].map((search) => (
              <a
                key={search}
                href={`/search?q=${encodeURIComponent(search)}`}
                className="px-4 py-2 bg-gray-100 hover:bg-orange-100 text-gray-700 hover:text-orange-700 rounded-full transition-colors duration-200"
              >
                {search}
              </a>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
