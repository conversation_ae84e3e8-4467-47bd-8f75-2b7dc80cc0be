{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/food/allrecipes-clone/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport <PERSON> from 'next/link';\nimport { Search, Menu, X, User, Heart, Plus } from 'lucide-react';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const navigationItems = [\n    {\n      name: 'Dinners',\n      href: '/dinners',\n      submenu: [\n        { name: '5-Ingredient Dinners', href: '/dinners/5-ingredient' },\n        { name: 'One-Pot Meals', href: '/dinners/one-pot' },\n        { name: 'Quick & Easy', href: '/dinners/quick-easy' },\n        { name: '30-Minute Meals', href: '/dinners/30-minute' },\n        { name: 'Family Dinners', href: '/dinners/family' },\n        { name: 'Comfort Food', href: '/dinners/comfort-food' }\n      ]\n    },\n    {\n      name: 'Me<PERSON>',\n      href: '/meals',\n      submenu: [\n        { name: 'Breakfast & Brunch', href: '/meals/breakfast-brunch' },\n        { name: 'Lunch', href: '/meals/lunch' },\n        { name: 'Healthy', href: '/meals/healthy' },\n        { name: 'Appetizers & Snacks', href: '/meals/appetizers-snacks' },\n        { name: 'Salads', href: '/meals/salads' },\n        { name: 'Side Dishes', href: '/meals/side-dishes' },\n        { name: 'Soups', href: '/meals/soups' },\n        { name: 'Desserts', href: '/meals/desserts' }\n      ]\n    },\n    {\n      name: 'Ingredients',\n      href: '/ingredients',\n      submenu: [\n        { name: 'Chicken', href: '/ingredients/chicken' },\n        { name: 'Beef', href: '/ingredients/beef' },\n        { name: 'Pork', href: '/ingredients/pork' },\n        { name: 'Seafood', href: '/ingredients/seafood' },\n        { name: 'Pasta', href: '/ingredients/pasta' },\n        { name: 'Vegetables', href: '/ingredients/vegetables' }\n      ]\n    },\n    {\n      name: 'Occasions',\n      href: '/occasions',\n      submenu: [\n        { name: 'Summer Recipes', href: '/occasions/summer' },\n        { name: 'Holiday Recipes', href: '/occasions/holidays' },\n        { name: 'Party Food', href: '/occasions/party' },\n        { name: 'Quick Weeknight', href: '/occasions/weeknight' }\n      ]\n    },\n    {\n      name: 'Cuisines',\n      href: '/cuisines',\n      submenu: [\n        { name: 'Italian', href: '/cuisines/italian' },\n        { name: 'Mexican', href: '/cuisines/mexican' },\n        { name: 'Chinese', href: '/cuisines/chinese' },\n        { name: 'Indian', href: '/cuisines/indian' },\n        { name: 'French', href: '/cuisines/french' },\n        { name: 'Japanese', href: '/cuisines/japanese' }\n      ]\n    },\n    {\n      name: 'Kitchen Tips',\n      href: '/kitchen-tips',\n      submenu: [\n        { name: 'Cooking Techniques', href: '/kitchen-tips/techniques' },\n        { name: 'Equipment Reviews', href: '/kitchen-tips/equipment' },\n        { name: 'Ingredient Substitutions', href: '/kitchen-tips/substitutions' },\n        { name: 'Meal Planning', href: '/kitchen-tips/meal-planning' }\n      ]\n    }\n  ];\n\n  return (\n    <header className=\"bg-white shadow-md sticky top-0 z-50\">\n      {/* Top Bar */}\n      <div className=\"bg-orange-500 text-white py-2\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center text-sm\">\n            <div className=\"flex space-x-4\">\n              <Link href=\"/newsletter\" className=\"hover:underline\">Newsletter</Link>\n              <Link href=\"/magazine\" className=\"hover:underline\">Magazine</Link>\n            </div>\n            <div className=\"flex space-x-4\">\n              <Link href=\"/login\" className=\"hover:underline\">Log In</Link>\n              <Link href=\"/signup\" className=\"hover:underline\">Sign Up</Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Header */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center\">\n            <span className=\"text-2xl font-bold text-orange-600\">AllRecipes</span>\n          </Link>\n\n          {/* Search Bar - Desktop */}\n          <div className=\"hidden md:flex flex-1 max-w-2xl mx-8\">\n            <div className=\"relative w-full\">\n              <input\n                type=\"text\"\n                placeholder=\"Find a recipe or ingredient...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full px-4 py-2 pl-10 pr-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n              />\n              <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n            </div>\n          </div>\n\n          {/* User Actions */}\n          <div className=\"flex items-center space-x-4\">\n            <button className=\"hidden md:flex items-center space-x-1 text-gray-700 hover:text-orange-600\">\n              <Plus className=\"h-5 w-5\" />\n              <span>Add Recipe</span>\n            </button>\n            <button className=\"text-gray-700 hover:text-orange-600\">\n              <Heart className=\"h-6 w-6\" />\n            </button>\n            <button className=\"text-gray-700 hover:text-orange-600\">\n              <User className=\"h-6 w-6\" />\n            </button>\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"lg:hidden text-gray-700 hover:text-orange-600\"\n            >\n              {isMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Navigation - Desktop */}\n        <nav className=\"hidden lg:block border-t border-gray-200\">\n          <div className=\"flex space-x-6 xl:space-x-8 py-4\">\n            {navigationItems.map((item) => (\n              <div key={item.name} className=\"relative group\">\n                <Link\n                  href={item.href}\n                  className=\"text-gray-700 hover:text-orange-600 font-medium transition-colors duration-200 text-sm xl:text-base\"\n                >\n                  {item.name}\n                </Link>\n                {/* Dropdown Menu */}\n                <div className=\"absolute left-0 mt-2 w-64 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                  <div className=\"py-2\">\n                    {item.submenu.map((subItem) => (\n                      <Link\n                        key={subItem.name}\n                        href={subItem.href}\n                        className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-600\"\n                      >\n                        {subItem.name}\n                      </Link>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </nav>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMenuOpen && (\n        <div className=\"lg:hidden bg-white border-t border-gray-200\">\n          {/* Mobile Search */}\n          <div className=\"px-4 py-3 border-b border-gray-200\">\n            <div className=\"relative\">\n              <input\n                type=\"text\"\n                placeholder=\"Find a recipe...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500\"\n              />\n              <Search className=\"absolute left-3 top-2.5 h-5 w-5 text-gray-400\" />\n            </div>\n          </div>\n\n          {/* Mobile Navigation */}\n          <div className=\"py-2\">\n            {navigationItems.map((item) => (\n              <div key={item.name}>\n                <Link\n                  href={item.href}\n                  className=\"block px-4 py-3 text-gray-700 hover:bg-orange-50 hover:text-orange-600 font-medium\"\n                >\n                  {item.name}\n                </Link>\n                <div className=\"pl-8\">\n                  {item.submenu.slice(0, 4).map((subItem) => (\n                    <Link\n                      key={subItem.name}\n                      href={subItem.href}\n                      className=\"block px-4 py-2 text-sm text-gray-600 hover:bg-orange-50 hover:text-orange-600\"\n                    >\n                      {subItem.name}\n                    </Link>\n                  ))}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,MAAM;YACN,SAAS;gBACP;oBAAE,MAAM;oBAAwB,MAAM;gBAAwB;gBAC9D;oBAAE,MAAM;oBAAiB,MAAM;gBAAmB;gBAClD;oBAAE,MAAM;oBAAgB,MAAM;gBAAsB;gBACpD;oBAAE,MAAM;oBAAmB,MAAM;gBAAqB;gBACtD;oBAAE,MAAM;oBAAkB,MAAM;gBAAkB;gBAClD;oBAAE,MAAM;oBAAgB,MAAM;gBAAwB;aACvD;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;gBACP;oBAAE,MAAM;oBAAsB,MAAM;gBAA0B;gBAC9D;oBAAE,MAAM;oBAAS,MAAM;gBAAe;gBACtC;oBAAE,MAAM;oBAAW,MAAM;gBAAiB;gBAC1C;oBAAE,MAAM;oBAAuB,MAAM;gBAA2B;gBAChE;oBAAE,MAAM;oBAAU,MAAM;gBAAgB;gBACxC;oBAAE,MAAM;oBAAe,MAAM;gBAAqB;gBAClD;oBAAE,MAAM;oBAAS,MAAM;gBAAe;gBACtC;oBAAE,MAAM;oBAAY,MAAM;gBAAkB;aAC7C;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;gBACP;oBAAE,MAAM;oBAAW,MAAM;gBAAuB;gBAChD;oBAAE,MAAM;oBAAQ,MAAM;gBAAoB;gBAC1C;oBAAE,MAAM;oBAAQ,MAAM;gBAAoB;gBAC1C;oBAAE,MAAM;oBAAW,MAAM;gBAAuB;gBAChD;oBAAE,MAAM;oBAAS,MAAM;gBAAqB;gBAC5C;oBAAE,MAAM;oBAAc,MAAM;gBAA0B;aACvD;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;gBACP;oBAAE,MAAM;oBAAkB,MAAM;gBAAoB;gBACpD;oBAAE,MAAM;oBAAmB,MAAM;gBAAsB;gBACvD;oBAAE,MAAM;oBAAc,MAAM;gBAAmB;gBAC/C;oBAAE,MAAM;oBAAmB,MAAM;gBAAuB;aACzD;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;gBACP;oBAAE,MAAM;oBAAW,MAAM;gBAAoB;gBAC7C;oBAAE,MAAM;oBAAW,MAAM;gBAAoB;gBAC7C;oBAAE,MAAM;oBAAW,MAAM;gBAAoB;gBAC7C;oBAAE,MAAM;oBAAU,MAAM;gBAAmB;gBAC3C;oBAAE,MAAM;oBAAU,MAAM;gBAAmB;gBAC3C;oBAAE,MAAM;oBAAY,MAAM;gBAAqB;aAChD;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS;gBACP;oBAAE,MAAM;oBAAsB,MAAM;gBAA2B;gBAC/D;oBAAE,MAAM;oBAAqB,MAAM;gBAA0B;gBAC7D;oBAAE,MAAM;oBAA4B,MAAM;gBAA8B;gBACxE;oBAAE,MAAM;oBAAiB,MAAM;gBAA8B;aAC9D;QACH;KACD;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAc,WAAU;kDAAkB;;;;;;kDACrD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAkB;;;;;;;;;;;;0CAErD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAkB;;;;;;kDAChD,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAU,WAAU;kDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOzD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,8OAAC;oCAAK,WAAU;8CAAqC;;;;;;;;;;;0CAIvD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;sDAEZ,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKtB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAO,WAAU;kDAChB,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,8OAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;qGAAe,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAMhE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,IAAI;;;;;;sDAGZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,wBACjB,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,QAAQ,IAAI;wDAClB,WAAU;kEAET,QAAQ,IAAI;uDAJR,QAAQ,IAAI;;;;;;;;;;;;;;;;mCAZjB,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;YA4B1B,4BACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;8CAEZ,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKtB,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC;;kDACC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBAC7B,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,QAAQ,IAAI;gDAClB,WAAU;0DAET,QAAQ,IAAI;+CAJR,QAAQ,IAAI;;;;;;;;;;;+BAVf,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;AAyBjC;uCAEe", "debugId": null}}]}